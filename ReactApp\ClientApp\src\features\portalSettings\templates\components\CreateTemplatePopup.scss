// Create Template Popup Styles
.create-template-popup {
  .k-dialog {
    width: 800px;
    max-width: 90vw;
    height: 500px;
    max-height: 80vh;
  }

  .k-dialog-titlebar {
    background-color: #007acc !important; // Blue header background
    color: white !important;
    border-bottom: 1px solid #005a99;
    padding: 16px 20px;

    .k-dialog-title {
      color: white !important;
      font-weight: 600;
      font-size: 18px;
      margin: 0;
    }

    .k-dialog-close {
      color: white !important;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1) !important;
      }
    }
  }

  .k-dialog-content {
    padding: 0;
    overflow: hidden;
    display: flex;
    height: calc(100% - 120px);
  }

  .popup-content {
    display: flex;
    width: 100%;
    height: 100%;

    .popup-left {
      flex: 1;
      padding: 20px;
      border-right: 1px solid #e0e0e0; // Border separation
      background-color: white; // Changed to white
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .popup-right {
      flex: 1;
      padding: 20px;
      background-color: white;
      display: flex;
      align-items: center;
      justify-content: center;

      .preview-placeholder {
        text-align: center;
        color: #666;
        font-style: italic;
      }
    }
  }

  .k-dialog-actions {
    padding: 16px 20px;
    border-top: 1px solid #e0e0e0;
    background-color: white;
    display: flex;
    justify-content: flex-end; // Align buttons to the right
    gap: 12px;

    .k-button {
      padding: 10px 20px;
      font-weight: 500;
      border-radius: 4px;
      min-width: 100px;

      &.k-button-solid-primary {
        background-color: #007acc !important;
        border-color: #007acc !important;
        color: white !important;

        &:hover {
          background-color: #005a99 !important;
          border-color: #005a99 !important;
        }
      }

      &.k-button-solid-base {
        background-color: transparent !important;
        border: none !important;
        color: #333 !important;

        &:hover {
          background-color: #f5f5f5 !important;
        }
      }
    }
  }
}

// Form Styles
.create-template-form {
  .form-group {
    margin-bottom: 16px; // Reduced spacing for better fit

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
  }

  .k-textbox {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;

    &.k-invalid {
      border-color: #dc3545;
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    &:focus {
      border-color: #007acc;
      box-shadow: 0 0 0 0.2rem rgba(0, 122, 204, 0.25);
    }
  }

  .validation-error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 4px;
    display: block;
  }

  .switch-group {
    .switch-container {
      display: flex;
      align-items: center;

      .k-switch {
        margin-right: 12px;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .create-template-popup {
    width: 95% !important;
    height: auto !important;
    
    .popup-content {
      flex-direction: column;
      height: auto;
      
      .popup-left {
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
      }
      
      .popup-right {
        min-height: 150px;
      }
    }
  }
}

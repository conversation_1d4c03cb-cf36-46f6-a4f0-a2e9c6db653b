import { ROUTES } from "@/constants/routes";
import type { MenuItem } from "@/types";

export const menuData: MenuItem[] = [
  {
    id: "portal",
    title: "Portal Settings",
    icon: "cog",
    children: [
      {
        title: "Terms & Conditions",
        route: ROUTES.TERMS_AND_CONDITIONS,
        icon: "file",
      },
      {
        title: "Portal Role Assignment",
        route: ROUTES.PORTAL_ROLE_ASSIGNMENT,
        icon: "file",
      },
      {
        title: "Folder Permissions",
        route: ROUTES.FOLDER_PERMISSIONS,
        icon: "file",
      },
      {
        title: "Templates",
        route: ROUTES.TEMPLATES,
        icon: "file",
      },
    ],
  },
  {
    id: "dms",
    title: "DMS Settings",
    icon: "folder",
    children: [
      {
        title: "Privileges Settings",
        route: ROUTES.PRIVILEGES,
        icon: "file",
      },
      {
        title: "Staff Settings",
        route: ROUTES.STAFF,
        icon: "file",
      },
      {
        title: "Dashboard Settings",
        route: ROUTES.DASHBOARD_SETTINGS,
        icon: "file",
      },
      {
        title: "AutoFiling Settings",
        route: ROUTES.AUTOFILL,
        icon: "file",
      },
    ],
  },
  {
    id: "shared",
    title: "Shared Settings",
    icon: "user",
    children: [
      {
        title: "Client Branding",
        route: ROUTES.BRANDING,
        icon: "file",
      },
      {
        title: "Email Format Settings",
        route: ROUTES.EMAIL_FORMAT,
        icon: "file",
      },
      {
        title: "Audit Logs",
        route: ROUTES.AUDIT_LOGS,
        icon: "file",
      },
    ],
  },
];

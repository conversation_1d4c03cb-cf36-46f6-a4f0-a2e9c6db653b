import { Dialog, DialogActionsBar } from "@progress/kendo-react-dialogs";
import { Button } from "@progress/kendo-react-buttons";
import { useTranslation } from "react-i18next";
import CreateTemplateForm from "./CreateTemplateForm";
import type { CreateTemplateFormData, CreateTemplateValidation } from "../hooks/useCreateTemplatePopup";

interface CreateTemplatePopupProps {
  isOpen: boolean;
  formData: CreateTemplateFormData;
  validation: CreateTemplateValidation;
  isSubmitting: boolean;
  onFieldChange: (field: keyof CreateTemplateFormData, value: string | boolean) => void;
  onCreate: () => void;
  onCancel: () => void;
}

export default function CreateTemplatePopup({
  isOpen,
  formData,
  validation,
  isSubmitting,
  onFieldChange,
  onCreate,
  onCancel,
}: CreateTemplatePopupProps) {
  const { t } = useTranslation("dashboard");

  if (!isOpen) return null;

  return (
    <Dialog
      onClose={onCancel}
      title="Create Template"
      className="create-template-popup"
      width={800}
      height={500}
    >
      <div className="popup-content">
        {/* Left Side - Form */}
        <div className="popup-left">
          <CreateTemplateForm
            formData={formData}
            validation={validation}
            onFieldChange={onFieldChange}
          />
        </div>

        {/* Right Side - Preview/Additional Content */}
        <div className="popup-right">
          <div className="preview-placeholder">
            {/* This area can be used for template preview or additional content */}
            <p>Template preview will appear here</p>
          </div>
        </div>
      </div>

      <DialogActionsBar>
        <Button
          onClick={onCancel}
          disabled={isSubmitting}
          fillMode="flat"
        >
          Cancel
        </Button>
        <Button
          themeColor="primary"
          onClick={onCreate}
          disabled={isSubmitting}
        >
          {isSubmitting ? "Creating..." : "Create"}
        </Button>
      </DialogActionsBar>
    </Dialog>
  );
}

import { useTranslation } from "react-i18next";
import "./Templates.scss";
import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import { Button } from "@progress/kendo-react-buttons";
import { CreateTemplatePopup } from "./components";
import { useCreateTemplatePopup } from "./hooks/useCreateTemplatePopup";
import "./components/CreateTemplatePopup.scss";

export default function TemplatesPage() {
  const { t } = useTranslation("dashboard");

  const {
    isOpen,
    formData,
    validation,
    isSubmitting,
    openPopup,
    updateField,
    handleCreate,
    handleCancel,
  } = useCreateTemplatePopup();

  return (
    <>
      <SectionLayout
        headerActions={
          <Button
            size="small"
            className="header-action-btn"
            icon="add"
            themeColor="base"
            onClick={openPopup}
          >
            {t("btn.createTemplate")}
          </Button>
        }
      >
        <div>Templates Grid View Comes here</div>
      </SectionLayout>

      <CreateTemplatePopup
        isOpen={isOpen}
        formData={formData}
        validation={validation}
        isSubmitting={isSubmitting}
        onFieldChange={updateField}
        onCreate={handleCreate}
        onCancel={handleCancel}
      />
    </>
  );
}

import { useState } from "react";

export interface CreateTemplateFormData {
  name: string;
  description: string;
  isActive: boolean;
}

export interface CreateTemplateValidation {
  nameError: string;
}

export function useCreateTemplatePopup() {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState<CreateTemplateFormData>({
    name: "",
    description: "",
    isActive: true, // Default to active/on as per requirements
  });
  const [validation, setValidation] = useState<CreateTemplateValidation>({
    nameError: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const openPopup = () => {
    setIsOpen(true);
    // Reset form when opening
    setFormData({
      name: "",
      description: "",
      isActive: true,
    });
    setValidation({
      nameError: "",
    });
  };

  const closePopup = () => {
    setIsOpen(false);
    setIsSubmitting(false);
  };

  const updateField = (field: keyof CreateTemplateFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear validation error when user starts typing in name field
    if (field === "name" && validation.nameError) {
      setValidation(prev => ({
        ...prev,
        nameError: "",
      }));
    }
  };

  const validateForm = (): boolean => {
    const errors: CreateTemplateValidation = {
      nameError: "",
    };

    // Validate name field - required
    if (!formData.name.trim()) {
      errors.nameError = "Template Name is mandatory";
    }

    setValidation(errors);

    // Return true if no errors
    return !errors.nameError;
  };

  const handleCreate = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // TODO: Implement actual API call when backend is ready
      console.log("Creating template:", formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Close popup on success
      closePopup();
      
      // TODO: Show success message and refresh templates list
    } catch (error) {
      console.error("Failed to create template:", error);
      // TODO: Show error message
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    closePopup();
  };

  return {
    isOpen,
    formData,
    validation,
    isSubmitting,
    openPopup,
    closePopup,
    updateField,
    handleCreate,
    handleCancel,
  };
}

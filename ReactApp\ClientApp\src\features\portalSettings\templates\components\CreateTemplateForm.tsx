import { Input, Switch } from "@progress/kendo-react-inputs";
import { useTranslation } from "react-i18next";
import type { CreateTemplateFormData, CreateTemplateValidation } from "../hooks/useCreateTemplatePopup";

interface CreateTemplateFormProps {
  formData: CreateTemplateFormData;
  validation: CreateTemplateValidation;
  onFieldChange: (field: keyof CreateTemplateFormData, value: string | boolean) => void;
}

export default function CreateTemplateForm({
  formData,
  validation,
  onFieldChange,
}: CreateTemplateFormProps) {
  const { t } = useTranslation("dashboard");

  return (
    <div className="create-template-form">
      {/* Name Field */}
      <div className="form-group">
        <label htmlFor="template-name" className="form-label">
          Name*
        </label>
        <Input
          id="template-name"
          name="name"
          value={formData.name}
          onChange={(e) => onFieldChange("name", e.value)}
          placeholder="Enter template name"
          className={validation.nameError ? "k-invalid" : ""}
          style={{ width: "100%" }}
        />
        {validation.nameError && (
          <div className="validation-error">
            {validation.nameError}
          </div>
        )}
      </div>

      {/* Description Field */}
      <div className="form-group">
        <label htmlFor="template-description" className="form-label">
          Description
        </label>
        <Input
          id="template-description"
          name="description"
          value={formData.description}
          onChange={(e) => onFieldChange("description", e.value)}
          placeholder="Enter description"
          style={{ width: "100%" }}
        />
      </div>

      {/* Active Switch */}
      <div className="form-group switch-group">
        <div className="switch-container">
          <Switch
            checked={formData.isActive}
            onChange={(e) => onFieldChange("isActive", e.value)}
            onLabel="Active"
            offLabel="Inactive"
          />
        </div>
      </div>
    </div>
  );
}
